#!/usr/bin/env python3
"""
Get Supabase Authentication Token
Simple script to create a user and get a JWT token for testing
"""

import os
import json
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def get_supabase_token():
    """Get a Supabase authentication token"""
    
    # Initialize Supabase client
    supabase_url = os.getenv('SUPABASE_URL')
    supabase_key = os.getenv('SUPABASE_KEY')
    
    if not supabase_url or not supabase_key:
        print("❌ SUPABASE_URL and SUPABASE_KEY must be set in .env file")
        return None
    
    supabase: Client = create_client(supabase_url, supabase_key)
    
    # Test credentials
    test_email = "<EMAIL>"
    test_password = "TestPassword123!"
    
    print("🔐 Getting Supabase Authentication Token...")
    print(f"📧 Email: {test_email}")
    print(f"🔑 Password: {test_password}")
    
    # Try to sign in first
    try:
        print("\n🔄 Attempting to sign in...")
        response = supabase.auth.sign_in_with_password({
            "email": test_email,
            "password": test_password
        })
        
        if response.session and response.session.access_token:
            print("✅ Successfully signed in!")
            print(f"🎫 Token: {response.session.access_token}")
            print(f"👤 User ID: {response.user.id}")
            return response.session.access_token
            
    except Exception as e:
        print(f"⚠️ Sign in failed: {e}")
        print("🔄 Attempting to create new user...")
        
        # Try to sign up
        try:
            response = supabase.auth.sign_up({
                "email": test_email,
                "password": test_password
            })
            
            if response.session and response.session.access_token:
                print("✅ Successfully created user and signed in!")
                print(f"🎫 Token: {response.session.access_token}")
                print(f"👤 User ID: {response.user.id}")
                return response.session.access_token
            elif response.user:
                print("✅ User created but needs email confirmation")
                print("📧 Check your email for confirmation link")
                print("⚠️ Cannot get token until email is confirmed")
                return None
            else:
                print("❌ Failed to create user")
                return None
                
        except Exception as signup_error:
            print(f"❌ Sign up failed: {signup_error}")
            return None

def test_token_with_api(token):
    """Test the token with a protected API endpoint"""
    import requests
    
    if not token:
        print("❌ No token to test")
        return
    
    print(f"\n🧪 Testing token with protected API endpoint...")
    
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {token}'
    }
    
    test_data = {
        "transcript": "I had a wonderful day today. I feel grateful and accomplished.",
        "entryId": "test-entry-123"
    }
    
    try:
        response = requests.post(
            "http://localhost:5000/api/analyze",
            headers=headers,
            json=test_data
        )
        
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Token works! API call successful")
            result = response.json()
            print(f"📝 Response: {json.dumps(result, indent=2)}")
        elif response.status_code == 401:
            print("❌ Token is invalid or expired")
        else:
            print(f"⚠️ Unexpected response: {response.status_code}")
            print(f"📝 Response: {response.text}")
            
    except Exception as e:
        print(f"❌ API test failed: {e}")

def save_token_to_file(token):
    """Save token to a file for easy reuse"""
    if not token:
        return
    
    token_file = "test_token.txt"
    try:
        with open(token_file, 'w') as f:
            f.write(token)
        print(f"💾 Token saved to {token_file}")
        print(f"📋 You can use this token in your tests:")
        print(f"   export TEST_TOKEN='{token}'")
    except Exception as e:
        print(f"❌ Failed to save token: {e}")

def show_usage_examples(token):
    """Show usage examples with the token"""
    if not token:
        return
    
    print(f"\n📚 Usage Examples:")
    print("=" * 60)
    
    print(f"\n🔧 cURL Example:")
    print(f"""curl -X POST http://localhost:5000/api/analyze \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer {token}" \\
  -d '{{"transcript": "I feel happy today!", "entryId": "test-123"}}' | jq .""")
    
    print(f"\n🐍 Python Example:")
    print(f"""import requests

headers = {{
    'Content-Type': 'application/json',
    'Authorization': 'Bearer {token}'
}}

response = requests.post(
    'http://localhost:5000/api/analyze',
    headers=headers,
    json={{"transcript": "Test message"}}
)

print(response.json())""")
    
    print(f"\n🌐 JavaScript Example:")
    print(f"""fetch('http://localhost:5000/api/analyze', {{
  method: 'POST',
  headers: {{
    'Content-Type': 'application/json',
    'Authorization': 'Bearer {token}'
  }},
  body: JSON.stringify({{
    transcript: 'Test message'
  }})
}})
.then(response => response.json())
.then(data => console.log(data));""")

def main():
    """Main function"""
    print("🚀 Supabase Token Generator for Sentari API Testing")
    print("=" * 60)
    
    # Get token
    token = get_supabase_token()
    
    if token:
        # Test token
        test_token_with_api(token)
        
        # Save token
        save_token_to_file(token)
        
        # Show examples
        show_usage_examples(token)
        
        print(f"\n🎉 Success! You now have a valid authentication token.")
        print(f"🔑 Token: {token[:50]}...")
        
    else:
        print(f"\n❌ Could not get authentication token.")
        print(f"💡 Possible solutions:")
        print(f"   1. Check your Supabase project settings")
        print(f"   2. Enable email signup in Supabase Auth settings")
        print(f"   3. Disable email confirmation if testing locally")
        print(f"   4. Use Supabase dashboard to create a user manually")

if __name__ == "__main__":
    main()
