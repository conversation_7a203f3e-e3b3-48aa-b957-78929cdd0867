# Environment
ENVIRONMENT=production
FLASK_ENV=production

# Flask Configuration
FLASK_SECRET_KEY=your-super-secret-key-change-this-in-production
FLASK_DEBUG=false

# Database Configuration
SUPABASE_URL=your-supabase-url
SUPABASE_KEY=your-supabase-anon-key
SUPABASE_DATABASE_URL=your-supabase-database-url

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key
OPENAI_CHAT_MODEL=gpt-4o-mini
OPENAI_EMBED_MODEL=text-embedding-3-small
OPENAI_WHISPER_MODEL=whisper-1

# Security Configuration
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
MAX_CONTENT_LENGTH=16777216

# Rate Limiting
RATE_LIMIT_DEFAULT="100 per minute"
RATE_LIMIT_STORAGE_URL=redis://redis:6379/0

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json

# Database Pool Configuration
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20

# Performance Configuration
WORKER_PROCESSES=4
WORKER_THREADS=2

# Monitoring Configuration
ENABLE_METRICS=true
METRICS_PORT=9090

# Grafana Configuration
GRAFANA_PASSWORD=your-grafana-password

# Sentry Configuration (optional)
SENTRY_DSN=your-sentry-dsn

# Redis Configuration
REDIS_URL=redis://redis:6379/0 