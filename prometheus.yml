global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Sentari application
  - job_name: 'sentari'
    static_configs:
      - targets: ['web:5000']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  # Redis
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']

  # Node exporter (if you want system metrics)
  # - job_name: 'node'
  #   static_configs:
  #     - targets: ['node-exporter:9100'] 