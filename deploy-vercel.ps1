# Vercel Deployment Script for Sentari AI App
# Run this script to deploy your Flask app to Vercel

Write-Host "🚀 Starting Vercel deployment for Sentari AI App..." -ForegroundColor Green

# Check if Vercel CLI is installed
try {
    $vercelVersion = vercel --version
    Write-Host "✅ Vercel CLI found: $vercelVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Vercel CLI not found. Please install it first:" -ForegroundColor Red
    Write-Host "npm i -g vercel" -ForegroundColor Yellow
    exit 1
}

# Check if user is logged in
try {
    $user = vercel whoami
    Write-Host "✅ Logged in as: $user" -ForegroundColor Green
} catch {
    Write-Host "❌ Not logged in to Vercel. Please run 'vercel login' first." -ForegroundColor Red
    exit 1
}

# Check if .env file exists and warn about environment variables
if (Test-Path ".env") {
    Write-Host "⚠️  Found .env file. Remember to set environment variables in Vercel dashboard!" -ForegroundColor Yellow
    Write-Host "Required variables:" -ForegroundColor Cyan
    Write-Host "  - SUPABASE_URL" -ForegroundColor White
    Write-Host "  - SUPABASE_KEY" -ForegroundColor White
    Write-Host "  - OPENAI_API_KEY" -ForegroundColor White
    Write-Host "  - FLASK_SECRET_KEY" -ForegroundColor White
} else {
    Write-Host "⚠️  No .env file found. Make sure to set environment variables in Vercel dashboard." -ForegroundColor Yellow
}

# Deploy to Vercel
Write-Host "📦 Deploying to Vercel..." -ForegroundColor Green
try {
    vercel --prod
    Write-Host "✅ Deployment completed successfully!" -ForegroundColor Green
} catch {
    Write-Host "❌ Deployment failed. Check the error messages above." -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "🎉 Your app is now deployed!" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Set environment variables in Vercel dashboard" -ForegroundColor White
Write-Host "2. Test your endpoints:" -ForegroundColor White
Write-Host "   - Health check: curl https://your-app.vercel.app/health" -ForegroundColor Gray
Write-Host "   - Main endpoint: curl https://your-app.vercel.app/" -ForegroundColor Gray
Write-Host "3. Update CORS_ORIGINS if you have a frontend" -ForegroundColor White
Write-Host ""
Write-Host "For more details, see VERCEL_DEPLOYMENT.md" -ForegroundColor Yellow 