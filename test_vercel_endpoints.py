#!/usr/bin/env python3
"""
Test Vercel endpoints to verify the fix
"""

import requests
import json

VERCEL_URL = "https://vv1-sentari-app.vercel.app"

def test_endpoint(endpoint, method="GET", data=None):
    """Test a single endpoint on Vercel"""
    url = f"{VERCEL_URL}{endpoint}"
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, timeout=30)
        elif method.upper() == "POST":
            response = requests.post(url, json=data, timeout=30)
        
        print(f"🧪 {method} {endpoint}")
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print(f"   ✅ Success")
            try:
                result = response.json()
                if 'message' in result:
                    print(f"   Message: {result['message']}")
                elif 'success' in result:
                    print(f"   Success: {result['success']}")
            except:
                pass
        elif response.status_code == 404:
            print(f"   ❌ Not Found (404) - Endpoint not registered")
        else:
            print(f"   ⚠️ Status {response.status_code}")
            try:
                result = response.json()
                if 'error' in result:
                    print(f"   Error: {result['error']}")
            except:
                pass
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"🧪 {method} {endpoint}")
        print(f"   💥 Error: {e}")
        return False

def main():
    """Test all endpoints on Vercel"""
    print("🚀 Testing Vercel Endpoints After Fix")
    print("=" * 60)
    print(f"🌐 Base URL: {VERCEL_URL}")
    print()
    
    # Test basic endpoints
    print("📊 Basic Endpoints:")
    test_endpoint("/")
    test_endpoint("/health")
    
    print("\n🔓 Non-Auth Endpoints (should work now):")
    test_endpoint("/api/test-tags")
    test_endpoint("/api/test-openai")
    test_endpoint("/api/empathy", "POST", {"transcript": "I feel good today"})
    test_endpoint("/api/run", "POST", {"text": "Test message"})
    
    print("\n🔒 Auth-Required Endpoints (should return 401):")
    test_endpoint("/api/analyze", "POST", {"transcript": "test"})
    test_endpoint("/api/save-entry", "POST", {"text": "test"})
    
    print("\n🔧 Debug Endpoints:")
    test_endpoint("/api/debug")
    test_endpoint("/error")
    
    print("\n" + "=" * 60)
    print("🎯 Expected Results:")
    print("✅ Non-auth endpoints should return 200 (not 404)")
    print("🔒 Auth endpoints should return 401 (not 404)")
    print("📊 Debug endpoints should show registered endpoints")

if __name__ == "__main__":
    main()
