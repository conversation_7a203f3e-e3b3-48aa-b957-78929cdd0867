#!/usr/bin/env python3
"""
Simple script to test API endpoints
"""
import requests
import json

BASE_URL = "http://localhost:5000"

def test_endpoint(method, endpoint, data=None, description=""):
    """Test a single endpoint"""
    url = f"{BASE_URL}{endpoint}"
    print(f"\n{'='*60}")
    print(f"Testing: {description}")
    print(f"Method: {method}")
    print(f"URL: {url}")
    if data:
        print(f"Data: {json.dumps(data, indent=2)}")
    print(f"{'='*60}")
    
    try:
        if method.upper() == "GET":
            response = requests.get(url)
        elif method.upper() == "POST":
            response = requests.post(url, json=data)
        else:
            print(f"Unsupported method: {method}")
            return
            
        print(f"Status Code: {response.status_code}")
        print(f"Response:")
        print(json.dumps(response.json(), indent=2))
        
    except Exception as e:
        print(f"Error: {e}")

def main():
    """Run all API tests"""
    print("🚀 Starting API Tests...")
    
    # Test 1: Health check
    test_endpoint("GET", "/health", description="Health Check")
    
    # Test 2: Root endpoint
    test_endpoint("GET", "/", description="Root Endpoint")
    
    # Test 3: Empathy endpoint
    test_endpoint("POST", "/api/empathy", {
        "transcript": "I had a really tough day at work today. Everything went wrong and I feel overwhelmed.",
        "userId": "test-user"
    }, description="Empathy Analysis")
    
    # Test 4: Run pipeline
    test_endpoint("POST", "/api/run", {
        "text": "Today I went for a walk in the park and felt really peaceful. The birds were singing and I had time to think about my goals.",
        "userId": "test-user"
    }, description="Run Pipeline")
    
    # Test 5: Test tags
    test_endpoint("GET", "/api/test-tags", description="Test Tags")
    
    # Test 6: Test OpenAI (will likely fail due to quota)
    test_endpoint("GET", "/api/test-openai", description="Test OpenAI")
    
    print(f"\n{'='*60}")
    print("✅ API Tests Complete!")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
