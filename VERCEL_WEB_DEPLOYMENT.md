# Vercel Web Deployment Guide - Step by Step

## 🎯 Overview
This guide will walk you through deploying your Sentari AI Flask app to Vercel using their web interface (no CLI required).

## 📋 Prerequisites
- ✅ Your code is in a Git repository (GitHub, GitLab, or Bitbucket)
- ✅ You have a Vercel account (free at [vercel.com](https://vercel.com))
- ✅ Your environment variables ready (see list below)

## 🚀 Step-by-Step Deployment

### Step 1: Go to Vercel Dashboard
1. Open your browser and go to [vercel.com](https://vercel.com)
2. Sign in to your Vercel account (or create one if you don't have it)
3. Click **"New Project"** or **"Add New..."** → **"Project"**

### Step 2: Import Your Repository
1. **Connect your Git provider** (GitHub, GitLab, or Bitbucket) if not already connected
2. **Find your repository**: Look for `vv1-Sentari-app` in the list
3. **Click on your repository** to select it
4. Click **"Import"**

### Step 3: Configure Project Settings
Vercel will automatically detect it's a Python project. Configure these settings:

#### Project Name
- **Project Name**: `sentari-ai-api` (or your preferred name)
- **Framework Preset**: Should auto-detect as "Other"
- **Root Directory**: Leave as `/` (root)

#### Build Settings
- **Build Command**: Leave empty (Vercel will auto-detect)
- **Output Directory**: Leave empty (not needed for API)
- **Install Command**: Leave empty (will use `pip install -r requirements.txt`)

### Step 4: Set Environment Variables
**⚠️ CRITICAL STEP** - Click **"Environment Variables"** and add these:

#### Required Variables:
```
SUPABASE_URL = your-supabase-project-url
SUPABASE_KEY = your-supabase-anon-key
OPENAI_API_KEY = your-openai-api-key
FLASK_SECRET_KEY = your-secure-secret-key
```

#### Optional Variables:
```
ENVIRONMENT = production
FLASK_ENV = production
CORS_ORIGINS = https://your-frontend-domain.com
OPENAI_CHAT_MODEL = gpt-4o-mini
OPENAI_EMBED_MODEL = text-embedding-3-small
OPENAI_WHISPER_MODEL = whisper-1
```

**How to add each variable:**
1. Click **"Add"** next to Environment Variables
2. **Name**: Enter the variable name (e.g., `SUPABASE_URL`)
3. **Value**: Enter the actual value
4. **Environment**: Select **"Production"** (and optionally "Preview" and "Development")
5. Click **"Add"**

### Step 5: Deploy
1. Click **"Deploy"** button
2. Wait for the build to complete (usually 2-5 minutes)
3. You'll see a success message with your deployment URL

## 🧪 Testing Your Deployment

### Check Your Deployment URL
Your app will be available at: `https://your-project-name.vercel.app`

### Test These Endpoints:
```bash
# Health check
curl https://your-project-name.vercel.app/health

# Main endpoint
curl https://your-project-name.vercel.app/

# Test OpenAI connection
curl -X POST https://your-project-name.vercel.app/api/test-openai
```

## 🔧 Troubleshooting Common Issues

### Issue 1: Build Fails
**Symptoms**: Red build status, error messages
**Solutions**:
- Check that `requirements.txt` exists and is valid
- Verify `vercel.json` is in the root directory
- Check that `app.py` is the main entry point

### Issue 2: Environment Variables Not Working
**Symptoms**: API calls fail with missing variable errors
**Solutions**:
- Double-check all environment variables are set in Vercel dashboard
- Make sure variable names match exactly (case-sensitive)
- Redeploy after adding variables

### Issue 3: Database Connection Issues
**Symptoms**: Supabase connection errors
**Solutions**:
- Verify `SUPABASE_URL` and `SUPABASE_KEY` are correct
- Check if Supabase allows connections from Vercel IPs
- Test database connection locally first

### Issue 4: CORS Errors
**Symptoms**: Frontend can't connect to API
**Solutions**:
- Add your frontend domain to `CORS_ORIGINS`
- Update the environment variable in Vercel dashboard
- Redeploy the application

## 📊 Monitoring Your Deployment

### Vercel Dashboard Features:
- **Functions**: Monitor serverless function performance
- **Analytics**: Track request volume and response times
- **Logs**: View real-time function logs
- **Deployments**: See deployment history and rollback if needed

### Health Monitoring:
- Use the `/health` endpoint to monitor app status
- Set up external monitoring (UptimeRobot, etc.) to ping the health endpoint

## 🔄 Updating Your App

### Automatic Deployments:
- Every push to your main branch will trigger a new deployment
- Vercel will automatically build and deploy your changes

### Manual Deployments:
1. Go to your project in Vercel dashboard
2. Click **"Deployments"** tab
3. Click **"Redeploy"** on any previous deployment

## 💰 Cost Considerations

### Free Tier Limits:
- **Function Execution**: 100GB-hours per month
- **Bandwidth**: 100GB per month
- **Build Minutes**: 6000 minutes per month

### Monitoring Usage:
- Check your usage in Vercel dashboard → Settings → Usage
- Monitor function execution times and frequency

## 🎉 Success Checklist

- [ ] Repository imported successfully
- [ ] All environment variables set
- [ ] Build completed without errors
- [ ] Health endpoint responding (`/health`)
- [ ] Main endpoint working (`/`)
- [ ] API endpoints tested
- [ ] Database connections working
- [ ] CORS configured (if you have a frontend)

## 📞 Getting Help

- **Vercel Documentation**: [vercel.com/docs](https://vercel.com/docs)
- **Vercel Support**: Available in dashboard
- **Community**: [github.com/vercel/vercel/discussions](https://github.com/vercel/vercel/discussions)

---

**Your Sentari AI app should now be live on Vercel! 🚀** 