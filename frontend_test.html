<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sentari API Frontend Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .endpoint-test {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
        }
        .endpoint-test h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .response {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            border-left: 4px solid #28a745;
        }
        .error {
            border-left: 4px solid #dc3545;
        }
        .loading {
            border-left: 4px solid #ffc107;
        }
        textarea {
            width: 100%;
            height: 80px;
            margin-bottom: 10px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: Arial, sans-serif;
        }
        input[type="text"] {
            width: 200px;
            padding: 8px;
            margin-right: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-online { background-color: #28a745; }
        .status-offline { background-color: #dc3545; }
        .status-loading { background-color: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Sentari API Frontend Test Suite</h1>
        <p>Test your backend API endpoints from a frontend perspective</p>
        
        <div class="endpoint-test">
            <h3><span id="server-status" class="status-indicator status-loading"></span>Server Status</h3>
            <button onclick="checkServerStatus()">Check Server Status</button>
            <div id="status-response" class="response"></div>
        </div>
    </div>

    <div class="container">
        <h2>📊 Basic Endpoints</h2>
        
        <div class="endpoint-test">
            <h3>Health Check</h3>
            <button onclick="testHealthCheck()">Test Health Endpoint</button>
            <div id="health-response" class="response"></div>
        </div>

        <div class="endpoint-test">
            <h3>Root Endpoint</h3>
            <button onclick="testRootEndpoint()">Test Root Endpoint</button>
            <div id="root-response" class="response"></div>
        </div>
    </div>

    <div class="container">
        <h2>🧠 AI Processing Endpoints</h2>
        
        <div class="endpoint-test">
            <h3>Empathy Analysis</h3>
            <textarea id="empathy-text" placeholder="Enter text for empathy analysis...">I had a really tough day at work today. Everything went wrong and I feel overwhelmed.</textarea>
            <br>
            <input type="text" id="empathy-user" placeholder="User ID" value="frontend-test-user">
            <button onclick="testEmpathyAnalysis()">Analyze Empathy</button>
            <div id="empathy-response" class="response"></div>
        </div>

        <div class="endpoint-test">
            <h3>Run Pipeline</h3>
            <textarea id="pipeline-text" placeholder="Enter text for pipeline processing...">Today I went for a walk in the park and felt really peaceful. The birds were singing and I had time to think about my goals.</textarea>
            <br>
            <input type="text" id="pipeline-user" placeholder="User ID" value="frontend-test-user">
            <button onclick="testRunPipeline()">Run Pipeline</button>
            <div id="pipeline-response" class="response"></div>
        </div>

        <div class="endpoint-test">
            <h3>Test Tags</h3>
            <button onclick="testTags()">Test Tag Classification</button>
            <div id="tags-response" class="response"></div>
        </div>

        <div class="endpoint-test">
            <h3>Test OpenAI</h3>
            <button onclick="testOpenAI()">Test OpenAI Connection</button>
            <div id="openai-response" class="response"></div>
        </div>
    </div>

    <div class="container">
        <h2>🔧 Utility Functions</h2>
        <button onclick="testAllEndpoints()">Test All Endpoints</button>
        <button onclick="clearAllResponses()">Clear All Responses</button>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000';
        
        // Utility function to make API calls
        async function makeApiCall(method, endpoint, data = null, responseElementId) {
            const responseElement = document.getElementById(responseElementId);
            responseElement.className = 'response loading';
            responseElement.textContent = 'Loading...';
            
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };
                
                if (data) {
                    options.body = JSON.stringify(data);
                }
                
                const response = await fetch(`${API_BASE}${endpoint}`, options);
                const result = await response.json();
                
                responseElement.className = response.ok ? 'response success' : 'response error';
                responseElement.textContent = `Status: ${response.status}\n\n${JSON.stringify(result, null, 2)}`;
                
                return { success: response.ok, data: result, status: response.status };
                
            } catch (error) {
                responseElement.className = 'response error';
                responseElement.textContent = `Error: ${error.message}`;
                return { success: false, error: error.message };
            }
        }
        
        // Test functions
        async function checkServerStatus() {
            const result = await makeApiCall('GET', '/health', null, 'status-response');
            const statusIndicator = document.getElementById('server-status');
            
            if (result.success) {
                statusIndicator.className = 'status-indicator status-online';
            } else {
                statusIndicator.className = 'status-indicator status-offline';
            }
        }
        
        async function testHealthCheck() {
            await makeApiCall('GET', '/health', null, 'health-response');
        }
        
        async function testRootEndpoint() {
            await makeApiCall('GET', '/', null, 'root-response');
        }
        
        async function testEmpathyAnalysis() {
            const text = document.getElementById('empathy-text').value;
            const userId = document.getElementById('empathy-user').value;
            
            if (!text.trim()) {
                alert('Please enter text for empathy analysis');
                return;
            }
            
            await makeApiCall('POST', '/api/empathy', {
                transcript: text,
                userId: userId
            }, 'empathy-response');
        }
        
        async function testRunPipeline() {
            const text = document.getElementById('pipeline-text').value;
            const userId = document.getElementById('pipeline-user').value;
            
            if (!text.trim()) {
                alert('Please enter text for pipeline processing');
                return;
            }
            
            await makeApiCall('POST', '/api/run', {
                text: text,
                userId: userId
            }, 'pipeline-response');
        }
        
        async function testTags() {
            await makeApiCall('GET', '/api/test-tags', null, 'tags-response');
        }
        
        async function testOpenAI() {
            await makeApiCall('GET', '/api/test-openai', null, 'openai-response');
        }
        
        async function testAllEndpoints() {
            console.log('Testing all endpoints...');
            await checkServerStatus();
            await testHealthCheck();
            await testRootEndpoint();
            await testEmpathyAnalysis();
            await testRunPipeline();
            await testTags();
            await testOpenAI();
            console.log('All endpoint tests completed!');
        }
        
        function clearAllResponses() {
            const responses = document.querySelectorAll('.response');
            responses.forEach(response => {
                response.textContent = '';
                response.className = 'response';
            });
        }
        
        // Auto-check server status on page load
        window.addEventListener('load', checkServerStatus);
    </script>
</body>
</html>
