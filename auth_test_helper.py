#!/usr/bin/env python3
"""
Authentication Test Helper for Sentari API
This script helps you get authentication tokens and test protected endpoints
"""

import os
import requests
import json
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class SupabaseAuthHelper:
    def __init__(self):
        self.supabase_url = os.getenv('SUPABASE_URL')
        self.supabase_key = os.getenv('SUPABASE_KEY')
        
        if not self.supabase_url or not self.supabase_key:
            raise ValueError("SUPABASE_URL and SUPABASE_KEY must be set in environment variables")
        
        self.supabase: Client = create_client(self.supabase_url, self.supabase_key)
        self.current_session = None

    def sign_up_user(self, email: str, password: str):
        """Sign up a new user"""
        try:
            response = self.supabase.auth.sign_up({
                "email": email,
                "password": password
            })
            print(f"✅ User signed up successfully: {email}")
            print(f"User ID: {response.user.id if response.user else 'None'}")
            print(f"Session: {'Yes' if response.session else 'No'}")
            return response
        except Exception as e:
            print(f"❌ Sign up failed: {e}")
            return None

    def sign_in_user(self, email: str, password: str):
        """Sign in an existing user"""
        try:
            response = self.supabase.auth.sign_in_with_password({
                "email": email,
                "password": password
            })
            self.current_session = response.session
            print(f"✅ User signed in successfully: {email}")
            print(f"Access Token: {response.session.access_token[:50]}...")
            print(f"User ID: {response.user.id}")
            return response
        except Exception as e:
            print(f"❌ Sign in failed: {e}")
            return None

    def get_current_token(self):
        """Get the current access token"""
        if self.current_session:
            return self.current_session.access_token
        return None

    def create_test_user(self, email: str = None, password: str = None):
        """Create a test user for API testing"""
        if not email:
            import random
            email = f"test-user-{random.randint(1000, 9999)}@example.com"
        if not password:
            password = "TestPassword123!"
        
        print(f"🔧 Creating test user: {email}")
        
        # Try to sign up
        signup_response = self.sign_up_user(email, password)
        
        if signup_response and signup_response.session:
            # User was created and signed in
            self.current_session = signup_response.session
            return {
                'email': email,
                'password': password,
                'user_id': signup_response.user.id,
                'token': signup_response.session.access_token
            }
        else:
            # Try to sign in (user might already exist)
            print("🔄 Trying to sign in existing user...")
            signin_response = self.sign_in_user(email, password)
            
            if signin_response and signin_response.session:
                return {
                    'email': email,
                    'password': password,
                    'user_id': signin_response.user.id,
                    'token': signin_response.session.access_token
                }
        
        return None

class APITester:
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.auth_helper = SupabaseAuthHelper()
        self.test_user = None
        self.auth_token = None

    def setup_test_user(self):
        """Set up a test user for authentication"""
        print("🚀 Setting up test user for authentication...")
        self.test_user = self.auth_helper.create_test_user()
        
        if self.test_user:
            self.auth_token = self.test_user['token']
            print(f"✅ Test user ready: {self.test_user['email']}")
            print(f"🔑 Token: {self.auth_token[:50]}...")
            return True
        else:
            print("❌ Failed to create test user")
            return False

    def make_authenticated_request(self, method, endpoint, data=None):
        """Make an authenticated API request"""
        if not self.auth_token:
            return {"error": "No authentication token available"}

        url = f"{self.base_url}{endpoint}"
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {self.auth_token}'
        }

        try:
            if method.upper() == "GET":
                response = requests.get(url, headers=headers)
            elif method.upper() == "POST":
                response = requests.post(url, headers=headers, json=data)
            elif method.upper() == "PUT":
                response = requests.put(url, headers=headers, json=data)
            elif method.upper() == "DELETE":
                response = requests.delete(url, headers=headers)
            else:
                return {"error": f"Unsupported method: {method}"}

            return {
                'status_code': response.status_code,
                'success': response.ok,
                'data': response.json() if response.content else None
            }

        except Exception as e:
            return {"error": str(e)}

    def test_protected_endpoints(self):
        """Test all protected endpoints"""
        if not self.auth_token:
            print("❌ No authentication token. Run setup_test_user() first.")
            return

        print("\n🔒 Testing Protected Endpoints...")
        print("=" * 60)

        # Test cases for protected endpoints
        test_cases = [
            {
                'method': 'POST',
                'endpoint': '/api/analyze',
                'data': {
                    'transcript': 'I had a great day today. I feel accomplished and happy.',
                    'entryId': 'test-entry-123'
                },
                'description': 'Analyze Transcript'
            },
            {
                'method': 'POST',
                'endpoint': '/api/save-entry',
                'data': {
                    'text': 'This is a test journal entry for saving.',
                    'language': 'en'
                },
                'description': 'Save Entry'
            },
            {
                'method': 'POST',
                'endpoint': '/api/emotion-trend',
                'data': {
                    'timeframe': 'week'
                },
                'description': 'Emotion Trend'
            },
            {
                'method': 'POST',
                'endpoint': '/api/pick-emoji',
                'data': {
                    'text': 'I feel excited about my new project!'
                },
                'description': 'Pick Emoji'
            },
            {
                'method': 'POST',
                'endpoint': '/api/pick-emoji-batch',
                'data': {
                    'entries': [
                        {'text': 'Happy day!', 'id': '1'},
                        {'text': 'Feeling sad', 'id': '2'}
                    ]
                },
                'description': 'Pick Emoji Batch'
            },
            {
                'method': 'POST',
                'endpoint': '/api/update-tags',
                'data': {
                    'entryId': 'test-entry-123',
                    'tags': ['happy', 'productive', 'reflection']
                },
                'description': 'Update Tags'
            },
            {
                'method': 'POST',
                'endpoint': '/api/update-transcript',
                'data': {
                    'entryId': 'test-entry-123',
                    'transcript': 'Updated transcript text'
                },
                'description': 'Update Transcript'
            }
        ]

        results = []
        for test_case in test_cases:
            print(f"\n🧪 Testing: {test_case['description']}")
            print(f"   {test_case['method']} {test_case['endpoint']}")
            
            result = self.make_authenticated_request(
                test_case['method'],
                test_case['endpoint'],
                test_case.get('data')
            )
            
            results.append({
                'test': test_case['description'],
                'endpoint': test_case['endpoint'],
                'result': result
            })
            
            if result.get('success'):
                print(f"   ✅ Success ({result['status_code']})")
            else:
                print(f"   ❌ Failed ({result.get('status_code', 'N/A')})")
                if 'error' in result:
                    print(f"   Error: {result['error']}")
                elif result.get('data'):
                    print(f"   Response: {result['data']}")

        return results

    def print_curl_commands(self):
        """Print cURL commands for testing protected endpoints"""
        if not self.auth_token:
            print("❌ No authentication token available")
            return

        print("\n📋 cURL Commands for Protected Endpoints:")
        print("=" * 60)
        
        commands = [
            {
                'name': 'Analyze',
                'command': f'''curl -X POST {self.base_url}/api/analyze \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer {self.auth_token}" \\
  -d '{{"transcript": "I had a great day today!", "entryId": "test-123"}}' | jq .'''
            },
            {
                'name': 'Save Entry',
                'command': f'''curl -X POST {self.base_url}/api/save-entry \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer {self.auth_token}" \\
  -d '{{"text": "Test journal entry", "language": "en"}}' | jq .'''
            },
            {
                'name': 'Pick Emoji',
                'command': f'''curl -X POST {self.base_url}/api/pick-emoji \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer {self.auth_token}" \\
  -d '{{"text": "I feel excited!"}}' | jq .'''
            }
        ]

        for cmd in commands:
            print(f"\n# {cmd['name']}:")
            print(cmd['command'])

def main():
    """Main function to run authentication tests"""
    print("🔐 Sentari API Authentication Test Helper")
    print("=" * 60)
    
    try:
        tester = APITester()
        
        # Set up test user
        if tester.setup_test_user():
            # Test protected endpoints
            results = tester.test_protected_endpoints()
            
            # Print cURL commands
            tester.print_curl_commands()
            
            # Summary
            print(f"\n📊 Test Summary:")
            print(f"Total tests: {len(results)}")
            successful = sum(1 for r in results if r['result'].get('success'))
            print(f"Successful: {successful}")
            print(f"Failed: {len(results) - successful}")
            
        else:
            print("❌ Could not set up test user. Check your Supabase configuration.")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        print("\n💡 Make sure your .env file has valid SUPABASE_URL and SUPABASE_KEY")

if __name__ == "__main__":
    main()
