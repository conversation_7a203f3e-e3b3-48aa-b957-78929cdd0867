#!/usr/bin/env python3
"""
Test script to verify the Vercel import fix
"""

import requests
import json
import time

VERCEL_URL = "https://vv1-sentari-app.vercel.app"

def test_endpoint(endpoint, method="GET", data=None, description=""):
    """Test a single endpoint"""
    url = f"{VERCEL_URL}{endpoint}"
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, timeout=30)
        elif method.upper() == "POST":
            response = requests.post(url, json=data, timeout=30)
        
        print(f"🧪 {description}")
        print(f"   {method} {endpoint}")
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print(f"   ✅ SUCCESS")
            try:
                result = response.json()
                if 'error' in result:
                    print(f"   📝 Response: {result['error']}")
                elif 'message' in result:
                    print(f"   📝 Response: {result['message']}")
                elif 'success' in result:
                    print(f"   📝 Success: {result['success']}")
            except:
                print(f"   📝 Response: {response.text[:100]}...")
        elif response.status_code == 404:
            print(f"   ❌ NOT FOUND (404) - Endpoint not registered")
        elif response.status_code == 500:
            print(f"   ⚠️ SERVER ERROR (500)")
            try:
                result = response.json()
                if 'error' in result:
                    print(f"   📝 Error: {result['error']}")
                if 'message' in result:
                    print(f"   📝 Message: {result['message']}")
            except:
                print(f"   📝 Response: {response.text[:100]}...")
        else:
            print(f"   ⚠️ Status {response.status_code}")
        
        return response.status_code
        
    except Exception as e:
        print(f"🧪 {description}")
        print(f"   💥 Error: {e}")
        return None

def main():
    """Test all endpoints after the fix"""
    print("🚀 Testing Vercel Endpoints After Import Fix")
    print("=" * 60)
    print(f"🌐 Base URL: {VERCEL_URL}")
    print(f"⏰ Time: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Test basic endpoints
    print("📊 Basic Endpoints:")
    test_endpoint("/", description="Root endpoint")
    test_endpoint("/health", description="Health check")
    
    print("\n🔓 Non-Auth Endpoints (should work now):")
    test_endpoint("/api/test-openai", description="Test OpenAI")
    test_endpoint("/api/test-tags", description="Test Tags")
    test_endpoint("/api/empathy", "POST", {"transcript": "I feel good today"}, "Empathy Analysis")
    test_endpoint("/api/run", "POST", {"text": "Test message"}, "Run Pipeline")
    test_endpoint("/api/whisper", "POST", {}, "Whisper (should return missing file error)")
    
    print("\n🔒 Auth-Required Endpoints (should return 401, not 404):")
    test_endpoint("/api/analyze", "POST", {"transcript": "test"}, "Analyze")
    test_endpoint("/api/save-entry", "POST", {"text": "test"}, "Save Entry")
    
    print("\n" + "=" * 60)
    print("🎯 Expected Results After Fix:")
    print("✅ Non-auth endpoints should return 200 or 500 (not 404)")
    print("🔒 Auth endpoints should return 401 (not 404)")
    print("📝 500 errors should show import details, not 'Not Found'")
    print("🚀 This confirms endpoints are registered and imports are working")

if __name__ == "__main__":
    main()
