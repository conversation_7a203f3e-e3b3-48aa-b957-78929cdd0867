/**
 * React Frontend API Integration Example
 * Shows how to integrate Sentari API with React components
 */

// API Service Class
class SentariAPIService {
    constructor(baseURL = 'http://localhost:5000') {
        this.baseURL = baseURL;
    }

    async makeRequest(method, endpoint, data = null) {
        const url = `${this.baseURL}${endpoint}`;
        const options = {
            method,
            headers: {
                'Content-Type': 'application/json',
            },
        };

        if (data) {
            options.body = JSON.stringify(data);
        }

        try {
            const response = await fetch(url, options);
            const result = await response.json();
            
            if (!response.ok) {
                throw new Error(`API Error: ${response.status} - ${result.error || 'Unknown error'}`);
            }
            
            return result;
        } catch (error) {
            console.error(`API Request failed: ${method} ${endpoint}`, error);
            throw error;
        }
    }

    // Health check
    async checkHealth() {
        return await this.makeRequest('GET', '/health');
    }

    // Empathy analysis
    async analyzeEmpathy(transcript, userId) {
        return await this.makeRequest('POST', '/api/empathy', {
            transcript,
            userId
        });
    }

    // Run pipeline
    async runPipeline(text, userId) {
        return await this.makeRequest('POST', '/api/run', {
            text,
            userId
        });
    }

    // Test tags
    async testTags() {
        return await this.makeRequest('GET', '/api/test-tags');
    }

    // Test OpenAI
    async testOpenAI() {
        return await this.makeRequest('GET', '/api/test-openai');
    }
}

// React Hook for API integration
function useSentariAPI() {
    const [api] = useState(() => new SentariAPIService());
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const callAPI = async (apiFunction) => {
        setLoading(true);
        setError(null);
        
        try {
            const result = await apiFunction();
            setLoading(false);
            return result;
        } catch (err) {
            setError(err.message);
            setLoading(false);
            throw err;
        }
    };

    return {
        api,
        loading,
        error,
        callAPI
    };
}

// React Component Example
function EmpathyAnalyzer() {
    const { api, loading, error, callAPI } = useSentariAPI();
    const [transcript, setTranscript] = useState('');
    const [result, setResult] = useState(null);
    const [userId] = useState('react-user-' + Math.random().toString(36).substr(2, 9));

    const handleAnalyze = async () => {
        if (!transcript.trim()) {
            alert('Please enter some text to analyze');
            return;
        }

        try {
            const analysisResult = await callAPI(() => 
                api.analyzeEmpathy(transcript, userId)
            );
            setResult(analysisResult);
        } catch (err) {
            console.error('Analysis failed:', err);
        }
    };

    return (
        <div className="empathy-analyzer">
            <h2>Empathy Analysis</h2>
            
            <div className="input-section">
                <textarea
                    value={transcript}
                    onChange={(e) => setTranscript(e.target.value)}
                    placeholder="Enter text for empathy analysis..."
                    rows={4}
                    cols={50}
                />
                <br />
                <button 
                    onClick={handleAnalyze} 
                    disabled={loading}
                >
                    {loading ? 'Analyzing...' : 'Analyze Empathy'}
                </button>
            </div>

            {error && (
                <div className="error">
                    Error: {error}
                </div>
            )}

            {result && (
                <div className="result">
                    <h3>Analysis Result</h3>
                    <p><strong>Empathy Score:</strong> {result.empathy_score}</p>
                    
                    <div>
                        <strong>Insights:</strong>
                        <ul>
                            {result.insights.map((insight, index) => (
                                <li key={index}>{insight}</li>
                            ))}
                        </ul>
                    </div>
                    
                    <div>
                        <strong>Suggestions:</strong>
                        <ul>
                            {result.suggestions.map((suggestion, index) => (
                                <li key={index}>{suggestion}</li>
                            ))}
                        </ul>
                    </div>
                </div>
            )}
        </div>
    );
}

// Pipeline Runner Component
function PipelineRunner() {
    const { api, loading, error, callAPI } = useSentariAPI();
    const [text, setText] = useState('');
    const [result, setResult] = useState(null);
    const [userId] = useState('react-user-' + Math.random().toString(36).substr(2, 9));

    const handleRunPipeline = async () => {
        if (!text.trim()) {
            alert('Please enter some text to process');
            return;
        }

        try {
            const pipelineResult = await callAPI(() => 
                api.runPipeline(text, userId)
            );
            setResult(pipelineResult);
        } catch (err) {
            console.error('Pipeline failed:', err);
        }
    };

    return (
        <div className="pipeline-runner">
            <h2>Pipeline Processing</h2>
            
            <div className="input-section">
                <textarea
                    value={text}
                    onChange={(e) => setText(e.target.value)}
                    placeholder="Enter text for pipeline processing..."
                    rows={4}
                    cols={50}
                />
                <br />
                <button 
                    onClick={handleRunPipeline} 
                    disabled={loading}
                >
                    {loading ? 'Processing...' : 'Run Pipeline'}
                </button>
            </div>

            {error && (
                <div className="error">
                    Error: {error}
                </div>
            )}

            {result && (
                <div className="result">
                    <h3>Pipeline Result</h3>
                    <p><strong>Entry ID:</strong> {result.entryId}</p>
                    <p><strong>Reply:</strong> {result.reply}</p>
                    <p><strong>Success:</strong> {result.success ? 'Yes' : 'No'}</p>
                </div>
            )}
        </div>
    );
}

// Health Status Component
function HealthStatus() {
    const { api, loading, error, callAPI } = useSentariAPI();
    const [status, setStatus] = useState(null);

    const checkHealth = async () => {
        try {
            const healthResult = await callAPI(() => api.checkHealth());
            setStatus(healthResult);
        } catch (err) {
            console.error('Health check failed:', err);
        }
    };

    // Check health on component mount
    useEffect(() => {
        checkHealth();
    }, []);

    return (
        <div className="health-status">
            <h2>Server Health Status</h2>
            
            <button onClick={checkHealth} disabled={loading}>
                {loading ? 'Checking...' : 'Check Health'}
            </button>

            {error && (
                <div className="error">
                    Server Error: {error}
                </div>
            )}

            {status && (
                <div className="status">
                    <p><strong>Status:</strong> 
                        <span className={status.status === 'healthy' ? 'healthy' : 'unhealthy'}>
                            {status.status}
                        </span>
                    </p>
                    <p><strong>Supabase:</strong> {status.supabase ? 'Connected' : 'Disconnected'}</p>
                    <p><strong>Environment:</strong> {status.vercel ? 'Vercel' : 'Local'}</p>
                    <p><strong>Timestamp:</strong> {new Date(status.timestamp).toLocaleString()}</p>
                </div>
            )}
        </div>
    );
}

// Main App Component
function SentariApp() {
    return (
        <div className="sentari-app">
            <h1>Sentari Frontend Integration Demo</h1>
            
            <div className="components-grid">
                <HealthStatus />
                <EmpathyAnalyzer />
                <PipelineRunner />
            </div>
            
            <style jsx>{`
                .sentari-app {
                    max-width: 1200px;
                    margin: 0 auto;
                    padding: 20px;
                    font-family: Arial, sans-serif;
                }
                
                .components-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
                    gap: 20px;
                    margin-top: 20px;
                }
                
                .empathy-analyzer, .pipeline-runner, .health-status {
                    border: 1px solid #ddd;
                    border-radius: 8px;
                    padding: 20px;
                    background: white;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                }
                
                .input-section {
                    margin-bottom: 20px;
                }
                
                textarea {
                    width: 100%;
                    padding: 10px;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    margin-bottom: 10px;
                }
                
                button {
                    background-color: #007bff;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 4px;
                    cursor: pointer;
                }
                
                button:disabled {
                    background-color: #6c757d;
                    cursor: not-allowed;
                }
                
                button:hover:not(:disabled) {
                    background-color: #0056b3;
                }
                
                .error {
                    color: #dc3545;
                    background-color: #f8d7da;
                    border: 1px solid #f5c6cb;
                    border-radius: 4px;
                    padding: 10px;
                    margin: 10px 0;
                }
                
                .result {
                    background-color: #d4edda;
                    border: 1px solid #c3e6cb;
                    border-radius: 4px;
                    padding: 15px;
                    margin: 10px 0;
                }
                
                .healthy {
                    color: #28a745;
                    font-weight: bold;
                }
                
                .unhealthy {
                    color: #dc3545;
                    font-weight: bold;
                }
            `}</style>
        </div>
    );
}

// Export for use in actual React app
export { SentariAPIService, useSentariAPI, EmpathyAnalyzer, PipelineRunner, HealthStatus, SentariApp };
