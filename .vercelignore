# Development files
.env
.env.local
.env.development
.env.test

# Docker files
Dockerfile
docker-compose.yml
docker-compose.dev.yml
.dockerignore

# Documentation
README*.md
VERCEL_DEPLOYMENT.md
ENVIRONMENT_VARIABLES.md

# Testing
test_*.py
*_test.py
tests/

# Logs
logs/
*.log

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# Deployment scripts
deploy.ps1
deploy.sh
deploy-vercel.ps1

# Monitoring
prometheus.yml
nginx.conf

# Postman collection
*.postman_collection.json

# Memory files
.remember/ 