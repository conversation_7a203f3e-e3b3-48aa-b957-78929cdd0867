# Environment Variables Quick Reference

## 🔑 Required Variables (Must Set in Vercel)

| Variable Name | Description | Example |
|---------------|-------------|---------|
| `SUPABASE_URL` | Your Supabase project URL | `https://your-project.supabase.co` |
| `SUPABASE_KEY` | Your Supabase anon key | `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...` |
| `OPENAI_API_KEY` | Your OpenAI API key | `sk-...` |
| `FLASK_SECRET_KEY` | Secure secret key for Flask | `your-super-secret-key-here` |

## 🔧 Optional Variables (Recommended)

| Variable Name | Description | Default Value |
|---------------|-------------|---------------|
| `ENVIRONMENT` | Set to production | `production` |
| `FLASK_ENV` | Flask environment | `production` |
| `CORS_ORIGINS` | Allowed origins (comma-separated) | `*` |
| `OPENAI_CHAT_MODEL` | OpenAI chat model | `gpt-4o-mini` |
| `OPENAI_EMBED_MODEL` | OpenAI embedding model | `text-embedding-3-small` |
| `OPENAI_WHISPER_MODEL` | OpenAI Whisper model | `whisper-1` |

## 📝 How to Add in Vercel Dashboard

1. Go to your project in Vercel dashboard
2. Click **"Settings"** tab
3. Click **"Environment Variables"** in the left sidebar
4. For each variable:
   - Click **"Add"**
   - **Name**: Enter variable name
   - **Value**: Enter the actual value
   - **Environment**: Select **"Production"**
   - Click **"Add"**

## ⚠️ Important Notes

- **Case Sensitive**: Variable names must match exactly
- **No Spaces**: Don't add spaces around the `=` sign
- **Secure**: Never commit these values to Git
- **Redeploy**: After adding variables, redeploy your app

## 🧪 Testing Variables

After deployment, test that variables are working:

```bash
# Test health endpoint
curl https://your-app.vercel.app/health

# Test OpenAI connection
curl -X POST https://your-app.vercel.app/api/test-openai
```

## 🔍 Where to Find Your Values

### Supabase
1. Go to your Supabase project dashboard
2. Click **"Settings"** → **"API"**
3. Copy **Project URL** and **anon public** key

### OpenAI
1. Go to [platform.openai.com](https://platform.openai.com)
2. Click **"API Keys"**
3. Create or copy an existing API key

### Flask Secret Key
Generate a secure random key:
```python
import secrets
print(secrets.token_hex(32))
``` 