#!/usr/bin/env python3
"""
Simple Authentication Test for Sentari API
Tests protected endpoints with manual token input
"""

import os
import requests
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

BASE_URL = "http://localhost:5000"

def test_protected_endpoint_without_auth():
    """Test a protected endpoint without authentication"""
    print("🔒 Testing protected endpoint WITHOUT authentication...")
    
    try:
        response = requests.post(f"{BASE_URL}/api/analyze", 
                               json={"transcript": "Test message"})
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 401:
            print("✅ Correctly returns 401 Unauthorized (as expected)")
        else:
            print("❌ Unexpected response")
            
    except Exception as e:
        print(f"Error: {e}")

def test_protected_endpoint_with_invalid_auth():
    """Test a protected endpoint with invalid authentication"""
    print("\n🔒 Testing protected endpoint WITH invalid authentication...")
    
    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer invalid-token-12345'
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/analyze", 
                               headers=headers,
                               json={"transcript": "Test message"})
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 401:
            print("✅ Correctly returns 401 Unauthorized for invalid token")
        else:
            print("❌ Unexpected response")
            
    except Exception as e:
        print(f"Error: {e}")

def test_with_manual_token():
    """Test with a manually provided token"""
    print("\n🔑 Testing with manual token...")
    print("To get a valid token:")
    print("1. Go to your Supabase dashboard")
    print("2. Go to Authentication > Users")
    print("3. Create a test user or use existing user")
    print("4. Get the JWT token from the user session")
    print("\nAlternatively, you can use Supabase client libraries to sign in and get a token.")
    
    # For demonstration, let's show what a successful request would look like
    token = input("\nEnter your Supabase JWT token (or press Enter to skip): ").strip()
    
    if not token:
        print("⏭️ Skipping manual token test")
        return
    
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {token}'
    }
    
    test_data = {
        "transcript": "I had a wonderful day today. I feel grateful and happy."
    }
    
    try:
        print(f"\n🧪 Testing /api/analyze with provided token...")
        response = requests.post(f"{BASE_URL}/api/analyze", 
                               headers=headers,
                               json=test_data)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 200:
            print("✅ Successfully authenticated and got response!")
        elif response.status_code == 401:
            print("❌ Token is invalid or expired")
        else:
            print(f"❓ Unexpected status code: {response.status_code}")
            
    except Exception as e:
        print(f"Error: {e}")

def show_supabase_auth_instructions():
    """Show instructions for getting Supabase auth tokens"""
    print("\n📚 How to get Supabase Authentication Tokens:")
    print("=" * 60)
    
    print("\n🔧 Method 1: Using Supabase Dashboard")
    print("1. Go to your Supabase project dashboard")
    print("2. Navigate to Authentication > Users")
    print("3. Create a new user or select existing user")
    print("4. Copy the JWT token from user details")
    
    print("\n🔧 Method 2: Using JavaScript (Frontend)")
    print("""
// In your frontend application
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  'YOUR_SUPABASE_URL',
  'YOUR_SUPABASE_ANON_KEY'
)

// Sign up
const { data, error } = await supabase.auth.signUp({
  email: '<EMAIL>',
  password: 'password123'
})

// Sign in
const { data, error } = await supabase.auth.signInWithPassword({
  email: '<EMAIL>',
  password: 'password123'
})

// Get token
const token = data.session?.access_token
console.log('Token:', token)
""")
    
    print("\n🔧 Method 3: Using Python (Backend)")
    print(f"""
from supabase import create_client

supabase = create_client(
    '{os.getenv('SUPABASE_URL', 'YOUR_SUPABASE_URL')}',
    '{os.getenv('SUPABASE_KEY', 'YOUR_SUPABASE_KEY')}'
)

# Sign in
response = supabase.auth.sign_in_with_password({{
    "email": "<EMAIL>",
    "password": "password123"
}})

token = response.session.access_token
print(f"Token: {{token}}")
""")

def show_curl_examples():
    """Show cURL examples for testing protected endpoints"""
    print("\n📋 cURL Examples for Protected Endpoints:")
    print("=" * 60)
    
    print("\n# Replace YOUR_JWT_TOKEN with actual token")
    
    examples = [
        {
            'name': 'Analyze Transcript',
            'endpoint': '/api/analyze',
            'data': '{"transcript": "I feel happy today!", "entryId": "test-123"}'
        },
        {
            'name': 'Save Entry',
            'endpoint': '/api/save-entry',
            'data': '{"text": "My journal entry", "language": "en"}'
        },
        {
            'name': 'Pick Emoji',
            'endpoint': '/api/pick-emoji',
            'data': '{"text": "I am excited about my project!"}'
        },
        {
            'name': 'Update Tags',
            'endpoint': '/api/update-tags',
            'data': '{"entryId": "test-123", "tags": ["happy", "productive"]}'
        }
    ]
    
    for example in examples:
        print(f"\n# {example['name']}:")
        print(f"""curl -X POST {BASE_URL}{example['endpoint']} \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \\
  -d '{example['data']}' | jq .""")

def test_non_protected_endpoints():
    """Test endpoints that don't require authentication"""
    print("\n✅ Testing NON-PROTECTED endpoints (should work):")
    print("=" * 60)
    
    endpoints = [
        {'method': 'GET', 'endpoint': '/health', 'description': 'Health Check'},
        {'method': 'GET', 'endpoint': '/', 'description': 'Root Endpoint'},
        {'method': 'POST', 'endpoint': '/api/empathy', 'data': {'transcript': 'I feel good'}, 'description': 'Empathy Analysis'},
        {'method': 'POST', 'endpoint': '/api/run', 'data': {'text': 'Test text'}, 'description': 'Run Pipeline'},
        {'method': 'GET', 'endpoint': '/api/test-tags', 'description': 'Test Tags'},
        {'method': 'GET', 'endpoint': '/api/test-openai', 'description': 'Test OpenAI'}
    ]
    
    for endpoint in endpoints:
        try:
            print(f"\n🧪 {endpoint['description']}: {endpoint['method']} {endpoint['endpoint']}")
            
            if endpoint['method'] == 'GET':
                response = requests.get(f"{BASE_URL}{endpoint['endpoint']}")
            else:
                response = requests.post(f"{BASE_URL}{endpoint['endpoint']}", 
                                       json=endpoint.get('data', {}))
            
            if response.ok:
                print(f"   ✅ Success ({response.status_code})")
            else:
                print(f"   ❌ Failed ({response.status_code})")
                
        except Exception as e:
            print(f"   💥 Error: {e}")

def main():
    """Main function"""
    print("🔐 Sentari API Authentication Testing")
    print("=" * 60)
    
    # Test non-protected endpoints first
    test_non_protected_endpoints()
    
    # Test protected endpoints without auth
    print("\n" + "=" * 60)
    test_protected_endpoint_without_auth()
    
    # Test protected endpoints with invalid auth
    test_protected_endpoint_with_invalid_auth()
    
    # Show instructions
    show_supabase_auth_instructions()
    
    # Show cURL examples
    show_curl_examples()
    
    # Test with manual token
    test_with_manual_token()
    
    print("\n" + "=" * 60)
    print("🎯 Summary:")
    print("- Non-protected endpoints should work without authentication")
    print("- Protected endpoints return 401 without valid JWT token")
    print("- Use Supabase dashboard or client libraries to get valid tokens")
    print("- Replace YOUR_JWT_TOKEN in cURL examples with real token")

if __name__ == "__main__":
    main()
