#!/usr/bin/env node
/**
 * Frontend API Test Suite
 * Simulates frontend API calls using JavaScript fetch
 */

const API_BASE = 'http://localhost:5000';

// Polyfill fetch for Node.js
if (typeof fetch === 'undefined') {
    global.fetch = require('node-fetch');
}

class FrontendAPITester {
    constructor() {
        this.results = [];
    }

    async makeApiCall(method, endpoint, data = null, description = '') {
        console.log(`\n🔄 Testing: ${description}`);
        console.log(`   Method: ${method}`);
        console.log(`   Endpoint: ${endpoint}`);
        
        if (data) {
            console.log(`   Data: ${JSON.stringify(data)}`);
        }

        try {
            const options = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                }
            };

            if (data) {
                options.body = JSON.stringify(data);
            }

            const startTime = Date.now();
            const response = await fetch(`${API_BASE}${endpoint}`, options);
            const endTime = Date.now();
            const responseTime = endTime - startTime;

            let result;
            try {
                result = await response.json();
            } catch (e) {
                result = await response.text();
            }

            const testResult = {
                description,
                method,
                endpoint,
                status: response.status,
                success: response.ok,
                responseTime,
                data: result
            };

            this.results.push(testResult);

            if (response.ok) {
                console.log(`   ✅ Success (${response.status}) - ${responseTime}ms`);
            } else {
                console.log(`   ❌ Failed (${response.status}) - ${responseTime}ms`);
            }

            console.log(`   Response: ${JSON.stringify(result, null, 2)}`);
            
            return testResult;

        } catch (error) {
            console.log(`   💥 Error: ${error.message}`);
            const testResult = {
                description,
                method,
                endpoint,
                status: 'ERROR',
                success: false,
                error: error.message
            };
            this.results.push(testResult);
            return testResult;
        }
    }

    async testHealthCheck() {
        return await this.makeApiCall('GET', '/health', null, 'Health Check');
    }

    async testRootEndpoint() {
        return await this.makeApiCall('GET', '/', null, 'Root Endpoint');
    }

    async testEmpathyAnalysis() {
        const data = {
            transcript: "I had a really tough day at work today. Everything went wrong and I feel overwhelmed.",
            userId: "frontend-test-user"
        };
        return await this.makeApiCall('POST', '/api/empathy', data, 'Empathy Analysis');
    }

    async testRunPipeline() {
        const data = {
            text: "Today I went for a walk in the park and felt really peaceful. The birds were singing and I had time to think about my goals.",
            userId: "frontend-test-user"
        };
        return await this.makeApiCall('POST', '/api/run', data, 'Run Pipeline');
    }

    async testTags() {
        return await this.makeApiCall('GET', '/api/test-tags', null, 'Test Tags');
    }

    async testOpenAI() {
        return await this.makeApiCall('GET', '/api/test-openai', null, 'Test OpenAI');
    }

    async testAuthenticatedEndpoint() {
        // Test an endpoint that requires authentication
        const data = {
            text: "This is a test entry for saving.",
            userId: "frontend-test-user"
        };
        return await this.makeApiCall('POST', '/api/analyze', data, 'Analyze (Auth Required)');
    }

    async testWhisperEndpoint() {
        // Test whisper endpoint (would normally send audio file)
        const data = {
            // This would normally be audio file data
            audio: "mock-audio-data"
        };
        return await this.makeApiCall('POST', '/api/whisper', data, 'Whisper Audio Transcription');
    }

    async runAllTests() {
        console.log('🚀 Starting Frontend API Test Suite...\n');
        console.log('=' * 60);

        // Basic endpoints
        await this.testHealthCheck();
        await this.testRootEndpoint();

        // AI processing endpoints
        await this.testEmpathyAnalysis();
        await this.testRunPipeline();
        await this.testTags();
        await this.testOpenAI();

        // Test authenticated endpoints (will fail without auth)
        await this.testAuthenticatedEndpoint();

        // Test other endpoints
        await this.testWhisperEndpoint();

        this.printSummary();
    }

    printSummary() {
        console.log('\n' + '=' * 60);
        console.log('📊 TEST SUMMARY');
        console.log('=' * 60);

        const successful = this.results.filter(r => r.success).length;
        const failed = this.results.filter(r => !r.success).length;
        const total = this.results.length;

        console.log(`Total Tests: ${total}`);
        console.log(`✅ Successful: ${successful}`);
        console.log(`❌ Failed: ${failed}`);
        console.log(`📈 Success Rate: ${((successful / total) * 100).toFixed(1)}%`);

        console.log('\n📋 Detailed Results:');
        this.results.forEach((result, index) => {
            const status = result.success ? '✅' : '❌';
            const responseTime = result.responseTime ? `(${result.responseTime}ms)` : '';
            console.log(`${index + 1}. ${status} ${result.description} - ${result.status} ${responseTime}`);
        });

        console.log('\n🔍 Frontend Integration Notes:');
        console.log('- All successful endpoints are ready for frontend integration');
        console.log('- Failed endpoints may need authentication or additional setup');
        console.log('- OpenAI endpoints will work once quota is resolved');
        console.log('- CORS is properly configured for frontend requests');
    }

    // Simulate real frontend usage patterns
    async simulateUserJourney() {
        console.log('\n🎭 Simulating User Journey...');
        
        // 1. Check if server is healthy
        const health = await this.testHealthCheck();
        if (!health.success) {
            console.log('❌ Server not healthy, stopping journey');
            return;
        }

        // 2. User submits text for empathy analysis
        console.log('\n👤 User submits text for empathy analysis...');
        await this.testEmpathyAnalysis();

        // 3. User runs the full pipeline
        console.log('\n🔄 User runs full pipeline...');
        await this.testRunPipeline();

        // 4. System tests tag classification
        console.log('\n🏷️ System tests tag classification...');
        await this.testTags();

        console.log('\n✅ User journey simulation complete!');
    }
}

// Main execution
async function main() {
    const tester = new FrontendAPITester();
    
    // Run all tests
    await tester.runAllTests();
    
    // Simulate user journey
    await tester.simulateUserJourney();
}

// Run if called directly
if (require.main === module) {
    main().catch(console.error);
}

module.exports = FrontendAPITester;
