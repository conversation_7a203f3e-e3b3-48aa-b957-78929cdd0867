# OpenAI API Key (Required)
OPENAI_API_KEY=sk-your-actual-openai-api-key-here

# Supabase Configuration (Required)
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_KEY=supabasekey
# OpenAI Model Configuration (Optional - will use defaults if not set)
OPENAI_CHAT_MODEL=gpt-4o-mini
OPENAI_EMBED_MODEL=text-embedding-3-small
OPENAI_WHISPER_MODEL=whisper-1

# Backend-Core Microservice Configuration (Optional)
BACKEND_CORE_URL=http://localhost:5001

# Flask Configuration (Optional)
FLASK_ENV=development
FLASK_DEBUG=True