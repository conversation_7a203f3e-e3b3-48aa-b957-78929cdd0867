# Vercel Deployment Guide for Sentari AI App

## Prerequisites

1. **Vercel Account**: Sign up at [vercel.com](https://vercel.com)
2. **Vercel CLI**: Install with `npm i -g vercel`
3. **Git Repository**: Your code should be in a Git repository

## Environment Variables Setup

Before deploying, you need to set up these environment variables in Vercel:

### Required Variables
- `SUPABASE_URL` - Your Supabase project URL
- `SUPABASE_KEY` - Your Supabase anon key
- `OPENAI_API_KEY` - Your OpenAI API key
- `FLASK_SECRET_KEY` - A secure secret key for Flask

### Optional Variables
- `SUPABASE_DATABASE_URL` - Direct database connection URL
- `CORS_ORIGINS` - Comma-separated list of allowed origins
- `OPENAI_CHAT_MODEL` - OpenAI model (default: gpt-4o-mini)
- `OPENAI_EMBED_MODEL` - Embedding model (default: text-embedding-3-small)
- `OPENAI_WHISPER_MODEL` - Whisper model (default: whisper-1)

## Deployment Steps

### 1. Install Vercel CLI
```bash
npm i -g vercel
```

### 2. Login to Vercel
```bash
vercel login
```

### 3. Deploy to Vercel
```bash
vercel
```

### 4. Set Environment Variables
After the first deployment, set your environment variables:

```bash
vercel env add SUPABASE_URL
vercel env add SUPABASE_KEY
vercel env add OPENAI_API_KEY
vercel env add FLASK_SECRET_KEY
```

Or use the Vercel dashboard:
1. Go to your project in Vercel dashboard
2. Navigate to Settings → Environment Variables
3. Add each variable with appropriate values

### 5. Redeploy with Environment Variables
```bash
vercel --prod
```

## Testing Your Deployment

### Health Check
```bash
curl https://your-app.vercel.app/health
```

### Basic Endpoint Test
```bash
curl https://your-app.vercel.app/
# Should return: Hello, Sentari!
```

### API Endpoint Test
```bash
curl -X POST https://your-app.vercel.app/api/test-openai
```

## Important Notes

### Serverless Limitations
- **Cold Starts**: First request may be slower
- **Timeout**: Functions have a 30-second timeout
- **Memory**: Limited to 1024MB by default
- **File System**: Read-only, no persistent storage

### Database Considerations
- Use Supabase connection pooling
- Keep connections short-lived
- Consider using connection pooling for better performance

### CORS Configuration
Make sure your `CORS_ORIGINS` includes your frontend domain:
```
https://your-frontend.vercel.app,https://your-custom-domain.com
```

## Troubleshooting

### Common Issues

1. **Environment Variables Not Set**
   - Check Vercel dashboard → Settings → Environment Variables
   - Redeploy after adding variables

2. **Database Connection Issues**
   - Verify Supabase URL and key
   - Check if Supabase allows connections from Vercel IPs

3. **Timeout Errors**
   - Optimize your code for faster execution
   - Consider breaking large operations into smaller chunks

4. **CORS Errors**
   - Update `CORS_ORIGINS` in environment variables
   - Include your frontend domain

### Debugging
- Check Vercel function logs in the dashboard
- Use the health endpoint to verify basic functionality
- Test endpoints individually to isolate issues

## Production Checklist

- [ ] All environment variables set
- [ ] CORS properly configured
- [ ] Health endpoint responding
- [ ] All API endpoints tested
- [ ] Database connections working
- [ ] OpenAI API calls successful
- [ ] Authentication working (if applicable)

## Monitoring

- Use Vercel's built-in analytics
- Monitor function execution times
- Set up alerts for errors
- Track API usage and costs

## Cost Optimization

- Use appropriate function timeout settings
- Optimize code for faster execution
- Monitor OpenAI API usage
- Consider caching strategies for repeated requests 