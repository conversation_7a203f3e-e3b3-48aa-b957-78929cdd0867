# Sentari API - cURL Command Reference

This document provides cURL commands to test all API endpoints from a frontend perspective.

## Base URL
```bash
BASE_URL="http://localhost:5000"
```

## 1. Health Check
```bash
curl -X GET "$BASE_URL/health" \
  -H "Content-Type: application/json" | jq .
```

## 2. Root Endpoint
```bash
curl -X GET "$BASE_URL/" \
  -H "Content-Type: application/json" | jq .
```

## 3. Empathy Analysis
```bash
curl -X POST "$BASE_URL/api/empathy" \
  -H "Content-Type: application/json" \
  -d '{
    "transcript": "I had a really tough day at work today. Everything went wrong and I feel overwhelmed.",
    "userId": "frontend-test-user"
  }' | jq .
```

## 4. Run Pipeline
```bash
curl -X POST "$BASE_URL/api/run" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Today I went for a walk in the park and felt really peaceful. The birds were singing and I had time to think about my goals.",
    "userId": "frontend-test-user"
  }' | jq .
```

## 5. Test Tags
```bash
curl -X GET "$BASE_URL/api/test-tags" \
  -H "Content-Type: application/json" | jq .
```

## 6. Test OpenAI
```bash
curl -X GET "$BASE_URL/api/test-openai" \
  -H "Content-Type: application/json" | jq .
```

## 7. Test Authenticated Endpoints (Will require auth)

### Analyze Endpoint
```bash
curl -X POST "$BASE_URL/api/analyze" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "text": "This is a test entry for analysis.",
    "userId": "frontend-test-user"
  }' | jq .
```

### Save Entry
```bash
curl -X POST "$BASE_URL/api/save-entry" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "text": "This is a test journal entry.",
    "userId": "frontend-test-user"
  }' | jq .
```

### Emotion Trend
```bash
curl -X POST "$BASE_URL/api/emotion-trend" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "userId": "frontend-test-user",
    "timeframe": "week"
  }' | jq .
```

### Pick Emoji
```bash
curl -X POST "$BASE_URL/api/pick-emoji" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "text": "I feel happy today!",
    "userId": "frontend-test-user"
  }' | jq .
```

### Update Tags
```bash
curl -X POST "$BASE_URL/api/update-tags" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "entryId": "entry_123",
    "tags": ["happy", "reflection"],
    "userId": "frontend-test-user"
  }' | jq .
```

## 8. Whisper Audio Transcription
```bash
# Note: This requires actual audio file upload
curl -X POST "$BASE_URL/api/whisper" \
  -H "Content-Type: multipart/form-data" \
  -F "audio=@path/to/audio/file.wav" | jq .
```

## 9. Batch Testing Script
```bash
#!/bin/bash

BASE_URL="http://localhost:5000"

echo "🚀 Testing Sentari API Endpoints..."

echo "1. Health Check:"
curl -s -X GET "$BASE_URL/health" | jq .

echo -e "\n2. Root Endpoint:"
curl -s -X GET "$BASE_URL/" | jq .

echo -e "\n3. Empathy Analysis:"
curl -s -X POST "$BASE_URL/api/empathy" \
  -H "Content-Type: application/json" \
  -d '{"transcript": "I feel overwhelmed today.", "userId": "test-user"}' | jq .

echo -e "\n4. Run Pipeline:"
curl -s -X POST "$BASE_URL/api/run" \
  -H "Content-Type: application/json" \
  -d '{"text": "Today was a good day.", "userId": "test-user"}' | jq .

echo -e "\n5. Test Tags:"
curl -s -X GET "$BASE_URL/api/test-tags" | jq .

echo -e "\n6. Test OpenAI:"
curl -s -X GET "$BASE_URL/api/test-openai" | jq .

echo -e "\n✅ API Testing Complete!"
```

## 10. Frontend JavaScript Examples

### Using Fetch API
```javascript
// Health check
const checkHealth = async () => {
  try {
    const response = await fetch('http://localhost:5000/health');
    const data = await response.json();
    console.log('Health:', data);
  } catch (error) {
    console.error('Health check failed:', error);
  }
};

// Empathy analysis
const analyzeEmpathy = async (transcript, userId) => {
  try {
    const response = await fetch('http://localhost:5000/api/empathy', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ transcript, userId })
    });
    const data = await response.json();
    console.log('Empathy analysis:', data);
    return data;
  } catch (error) {
    console.error('Empathy analysis failed:', error);
    throw error;
  }
};

// Run pipeline
const runPipeline = async (text, userId) => {
  try {
    const response = await fetch('http://localhost:5000/api/run', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ text, userId })
    });
    const data = await response.json();
    console.log('Pipeline result:', data);
    return data;
  } catch (error) {
    console.error('Pipeline failed:', error);
    throw error;
  }
};
```

### Using Axios
```javascript
import axios from 'axios';

const api = axios.create({
  baseURL: 'http://localhost:5000',
  headers: {
    'Content-Type': 'application/json',
  }
});

// Health check
const checkHealth = () => api.get('/health');

// Empathy analysis
const analyzeEmpathy = (transcript, userId) => 
  api.post('/api/empathy', { transcript, userId });

// Run pipeline
const runPipeline = (text, userId) => 
  api.post('/api/run', { text, userId });
```

## Expected Responses

### Successful Empathy Analysis
```json
{
  "empathy_score": 0.75,
  "insights": [
    "This appears to be a reflective moment",
    "The speaker shows emotional awareness",
    "There are elements of personal growth"
  ],
  "success": true,
  "suggestions": [
    "Consider journaling about this experience",
    "This could be a good topic for deeper reflection"
  ],
  "transcript": "I feel overwhelmed today.",
  "userId": "test-user"
}
```

### Successful Pipeline Run
```json
{
  "entryId": "entry_test-user_1234",
  "reply": "Processed: Today was a good day...",
  "success": true
}
```

### Health Check Response
```json
{
  "status": "healthy",
  "supabase": true,
  "timestamp": "2025-07-06T16:16:00.800580",
  "vercel": false
}
```

## Notes
- Replace `YOUR_JWT_TOKEN` with actual authentication token for protected endpoints
- Some endpoints require OpenAI credits to function fully
- CORS is configured to allow frontend requests
- All endpoints return JSON responses
