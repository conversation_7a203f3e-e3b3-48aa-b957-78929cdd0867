import os
import logging
import logging.config
from datetime import datetime
from flask import Flask, jsonify
from flask_cors import CORS
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from flask_compress import Compress
from flask_talisman import Talisman
from werkzeug.middleware.proxy_fix import ProxyFix
from werkzeug.middleware.dispatcher import DispatcherMiddleware

# Check if running on Vercel
is_vercel = os.getenv('VERCEL', '0') == '1'

# Import config with error handling
try:
    from .config import (
        validate_environment, get_cors_config, get_logging_config,
        IS_PRODUCTION, FLASK_SECRET_KEY, MAX_CONTENT_LENGTH,
        RATE_LIMIT_DEFAULT, RATE_LIMIT_STORAGE_URL, ENABLE_METRICS
    )
except Exception as e:
    # Fallback configuration for Vercel
    IS_PRODUCTION = os.getenv('ENVIRONMENT') == 'production'
    FLASK_SECRET_KEY = os.getenv('FLASK_SECRET_KEY', 'dev-secret-key')
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024
    RATE_LIMIT_DEFAULT = '100 per minute'
    RATE_LIMIT_STORAGE_URL = 'memory://'
    ENABLE_METRICS = False
    
    def validate_environment():
        """Minimal environment validation for Vercel."""
        pass
    
    def get_cors_config():
        """Minimal CORS config for Vercel."""
        return {
            'origins': '*',
            'methods': ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
            'allow_headers': ['Content-Type', 'Authorization']
        }
    
    def get_logging_config():
        """Minimal logging config for Vercel."""
        return {
            'version': 1,
            'disable_existing_loggers': False,
            'handlers': {
                'console': {
                    'class': 'logging.StreamHandler',
                    'level': 'INFO',
                    'stream': 'ext://sys.stdout'
                }
            },
            'loggers': {
                '': {
                    'handlers': ['console'],
                    'level': 'INFO',
                    'propagate': False
                }
            }
        }

def create_app(test_config=None):
    """Application factory pattern for creating Flask app instances."""
    
    # Create Flask app
    app = Flask(__name__, instance_relative_config=True)
    
    # Configure app
    if test_config is None:
        app.config.from_mapping(
            SECRET_KEY=FLASK_SECRET_KEY,
            MAX_CONTENT_LENGTH=MAX_CONTENT_LENGTH,
        )
    else:
        app.config.update(test_config)
    
    # Configure logging (skip file logging on Vercel)
    if not is_vercel:
        try:
            os.makedirs('logs')
            logging.config.dictConfig(get_logging_config())
        except Exception:
            # Fallback to basic logging
            logging.basicConfig(level=logging.INFO)
    else:
        # Simple logging for Vercel
        logging.basicConfig(level=logging.INFO)
    
    # Initialize Supabase client with error handling
    try:
        from supabase import create_client, Client
        supabase_url = os.getenv('SUPABASE_URL')
        supabase_key = os.getenv('SUPABASE_KEY')
        
        if supabase_url and supabase_key:
            supabase: Client = create_client(supabase_url, supabase_key)
            app.config['SUPABASE_CLIENT'] = supabase
        else:
            app.logger.warning("Supabase credentials not found")
            app.config['SUPABASE_CLIENT'] = None
    except Exception as e:
        app.logger.error(f"Failed to initialize Supabase: {e}")
        app.config['SUPABASE_CLIENT'] = None
    
    # Initialize OpenAI client with error handling
    try:
        import openai
        openai_api_key = os.getenv('OPENAI_API_KEY')
        if openai_api_key:
            openai.api_key = openai_api_key
        else:
            app.logger.warning("OpenAI API key not found")
    except Exception as e:
        app.logger.error(f"Failed to initialize OpenAI: {e}")
    
    # Configure CORS
    try:
        cors_config = get_cors_config()
        CORS(app, **cors_config)
    except Exception as e:
        app.logger.error(f"Failed to configure CORS: {e}")
        # Fallback CORS
        CORS(app)
    
    # Configure security headers (skip on Vercel if causing issues)
    if IS_PRODUCTION and not is_vercel:
        try:
            Talisman(
                app,
                content_security_policy={
                    'default-src': "'self'",
                    'script-src': "'self' 'unsafe-inline'",
                    'style-src': "'self' 'unsafe-inline'",
                    'img-src': "'self' data: https:",
                    'connect-src': "'self' https:",
                },
                force_https=True
            )
        except Exception as e:
            app.logger.error(f"Failed to configure Talisman: {e}")
    
    # Configure compression
    try:
        Compress(app)
    except Exception as e:
        app.logger.error(f"Failed to configure compression: {e}")
    
    # Configure rate limiting (use memory storage on Vercel)
    try:
        limiter = Limiter(
            app=app,
            key_func=get_remote_address,
            default_limits=[RATE_LIMIT_DEFAULT],
            storage_uri='memory://',  # Always use memory on Vercel
            strategy="fixed-window"
        )
        app.config['LIMITER'] = limiter
    except Exception as e:
        app.logger.error(f"Failed to configure rate limiting: {e}")
        app.config['LIMITER'] = None
    
    # Configure simple health check endpoint
    @app.route('/health')
    def health_check():
        return {
            'status': 'healthy', 
            'timestamp': datetime.now().isoformat(),
            'vercel': is_vercel,
            'supabase': app.config.get('SUPABASE_CLIENT') is not None
        }
    
    # Add root route
    @app.route('/')
    def root():
        return {
            'message': 'Hello, Sentari!',
            'status': 'running',
            'environment': os.getenv('ENVIRONMENT', 'development'),
            'vercel': is_vercel,
            'endpoints': {
                'health': '/health',
                'api': '/api/*'
            }
        }
    
    # Configure proxy fix for proper IP detection behind load balancers
    if IS_PRODUCTION and not is_vercel:
        try:
            app.wsgi_app = ProxyFix(
                app.wsgi_app,
                x_for=1,
                x_proto=1,
                x_host=1,
                x_prefix=1
            )
        except Exception as e:
            app.logger.error(f"Failed to configure ProxyFix: {e}")
    
    # Register blueprints with error handling
    try:
        register_blueprints(app)
    except Exception as e:
        app.logger.error(f"Failed to register blueprints: {e}")
    
    # Register error handlers
    register_error_handlers(app)
    
    # Add metrics endpoint if enabled (skip on Vercel)
    if ENABLE_METRICS and not is_vercel:
        try:
            from prometheus_client import make_wsgi_app
            app.wsgi_app = DispatcherMiddleware(app.wsgi_app, {
                '/metrics': make_wsgi_app()
            })
        except Exception as e:
            app.logger.error(f"Failed to configure metrics: {e}")
    
    return app

def register_blueprints(app):
    """Register all application blueprints."""
    try:
        from .entries import entries_bp
        from .embeddings import embeddings_bp
        from .profiles import profiles_bp
        from .tags import tags_bp
        from .core_pipeline import core_pipeline_bp
        
        app.register_blueprint(entries_bp)
        app.register_blueprint(embeddings_bp)
        app.register_blueprint(profiles_bp)
        app.register_blueprint(tags_bp)
        app.register_blueprint(core_pipeline_bp)
    except Exception as e:
        app.logger.error(f"Failed to register blueprints: {e}")

def register_error_handlers(app):
    """Register error handlers for the application."""
    
    @app.errorhandler(400)
    def bad_request(error):
        return jsonify({
            'error': 'Bad Request',
            'message': 'The request could not be processed.',
            'status_code': 400
        }), 400
    
    @app.errorhandler(401)
    def unauthorized(error):
        return jsonify({
            'error': 'Unauthorized',
            'message': 'Authentication required.',
            'status_code': 401
        }), 401
    
    @app.errorhandler(403)
    def forbidden(error):
        return jsonify({
            'error': 'Forbidden',
            'message': 'Access denied.',
            'status_code': 403
        }), 403
    
    @app.errorhandler(404)
    def not_found(error):
        return jsonify({
            'error': 'Not Found',
            'message': 'The requested resource was not found.',
            'status_code': 404
        }), 404
    
    @app.errorhandler(429)
    def too_many_requests(error):
        return jsonify({
            'error': 'Too Many Requests',
            'message': 'Rate limit exceeded.',
            'status_code': 429
        }), 429
    
    @app.errorhandler(500)
    def internal_error(error):
        app.logger.error(f'Server Error: {error}')
        return jsonify({
            'error': 'Internal Server Error',
            'message': 'An internal server error occurred.',
            'status_code': 500
        }), 500
    
    @app.errorhandler(Exception)
    def handle_exception(error):
        app.logger.error(f'Unhandled Exception: {error}')
        return jsonify({
            'error': 'Internal Server Error',
            'message': 'An unexpected error occurred.',
            'status_code': 500
        }), 500 