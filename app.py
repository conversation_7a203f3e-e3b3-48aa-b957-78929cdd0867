import os
import signal
import sys
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
from src.app_factory import create_app
from src.config import IS_PRODUCTION, WORKER_PROCESSES, WORKER_THREADS

# Check if running on Vercel
is_vercel = os.getenv('VERCEL', '0') == '1'

# Create the Flask application with error handling
try:
    app = create_app()
except Exception as e:
    # Create a minimal app for Vercel if the main app fails
    from flask import Flask, jsonify
    
    app = Flask(__name__)
    print("Error during app creation:", str(e), file=sys.stderr)
    
    @app.route('/')
    def hello():
        return 'Hello, Sentari!'
    
    @app.route('/health')
    def health_check():
        return {
            'status': 'healthy',
            'service': 'sentari-api',
            'environment': os.getenv('ENVIRONMENT', 'development'),
            'vercel': is_vercel
        }
    
    @app.route('/error')
    def show_error():
        return {
            'error': 'App initialization failed',
            'message': str(e),
            'vercel': is_vercel
        }, 500

# Import modular endpoints after app creation
# Always import basic endpoints, auth-required endpoints only if Supabase is available
always_import = True  # Always import non-auth endpoints
supabase_available = 'SUPABASE_CLIENT' in app.config and app.config.get('SUPABASE_CLIENT') is not None

if always_import:
    try:
        from src.analyze import analyze_endpoint
        from src.save_entry import save_entry_endpoint
        from src.emotion_trend import emotion_trend_endpoint
        from src.pick_emoji import pick_emoji_endpoint
        from src.pick_emoji_batch import pick_emoji_batch_endpoint
        from src.run_pipeline import run_pipeline_endpoint
        from src.empathy import empathy_endpoint
        from src.update_tags import update_tags_endpoint
        from src.update_transcript import update_transcript_endpoint
        from src.test_openai import test_openai_endpoint
        from src.test_tags import test_tags_endpoint
        from src.whisper import whisper_endpoint
        from src.auth import require_auth, get_user_id_from_request, get_user_email_from_request

        # Import new database API blueprints
        from src.entries import entries_bp
        from src.embeddings import embeddings_bp
        from src.profiles import profiles_bp
        from src.tags import tags_bp

        # Import core pipeline blueprint
        from src.core_pipeline import core_pipeline_bp

        # Get Supabase client from app config
        supabase = app.config.get('SUPABASE_CLIENT')

        # Always register non-auth endpoints (work without database)
        @app.route('/api/run', methods=['POST'])
        def run_pipeline():
            return run_pipeline_endpoint()

        @app.route('/api/empathy', methods=['POST'])
        def empathy():
            return empathy_endpoint()

        @app.route('/api/test-openai', methods=['GET'])
        def test_openai():
            return test_openai_endpoint()

        @app.route('/api/test-tags', methods=['GET', 'POST'])
        def test_tags():
            return test_tags_endpoint()

        # Whisper endpoint (audio transcription) - using modular implementation
        @app.route('/api/whisper', methods=['POST'])
        def whisper():
            return whisper_endpoint()

        # Register auth-required endpoints only if Supabase is available
        if supabase_available:
            @app.route('/api/analyze', methods=['POST'])
            @require_auth
            def analyze():
                user_id = get_user_id_from_request()
                return analyze_endpoint(supabase, user_id)

            @app.route('/api/save-entry', methods=['POST'])
            @require_auth
            def save_entry():
                user_id = get_user_id_from_request()
                return save_entry_endpoint(supabase, user_id)

            @app.route('/api/emotion-trend', methods=['POST'])
            @require_auth
            def emotion_trend():
                user_id = get_user_id_from_request()
                return emotion_trend_endpoint(supabase, user_id)

            @app.route('/api/pick-emoji', methods=['POST'])
            @require_auth
            def pick_emoji():
                user_id = get_user_id_from_request()
                return pick_emoji_endpoint(supabase, user_id)

            @app.route('/api/pick-emoji-batch', methods=['POST'])
            @require_auth
            def pick_emoji_batch():
                user_id = get_user_id_from_request()
                return pick_emoji_batch_endpoint(supabase, user_id)

            @app.route('/api/update-tags', methods=['POST'])
            @require_auth
            def update_tags():
                user_id = get_user_id_from_request()
                user_email = get_user_email_from_request()
                return update_tags_endpoint(supabase, user_id, user_email)

            @app.route('/api/update-transcript', methods=['POST'])
            @require_auth
            def update_transcript():
                user_id = get_user_id_from_request()
                return update_transcript_endpoint(supabase, user_id)
            
    except Exception as e:
        # If endpoints fail to load, add a debug endpoint
        @app.route('/api/debug')
        def debug():
            return {
                'error': 'Endpoints failed to load',
                'message': str(e),
                'vercel': is_vercel
            }, 500

# Register debug endpoints for Vercel
if is_vercel:
    try:
        @app.route('/api/debug', methods=['GET'])
        def debug_vercel():
            return {
                'status': 'debug endpoint available',
                'vercel': True,
                'environment': os.getenv('ENVIRONMENT', 'development'),
                'supabase_available': app.config.get('SUPABASE_CLIENT') is not None,
                'config_keys': list(app.config.keys()),
                'endpoints_registered': {
                    'non_auth': ['empathy', 'run', 'test-openai', 'test-tags', 'whisper'],
                    'auth_required': ['analyze', 'save-entry', 'emotion-trend', 'pick-emoji', 'update-tags'] if supabase_available else []
                }
            }

        @app.route('/error')
        def error_page():
            return {
                'message': 'Error endpoint available',
                'vercel': True,
                'environment': os.getenv('ENVIRONMENT', 'development'),
                'app_config': {
                    'supabase_available': app.config.get('SUPABASE_CLIENT') is not None,
                    'config_keys': list(app.config.keys())
                }
            }

    except Exception as e:
        # Final fallback - create a minimal debug endpoint
        @app.route('/api/debug')
        def minimal_debug():
            return {
                'error': 'Failed to register even basic endpoints',
                'message': str(e),
                'vercel': True
            }, 500
            

def signal_handler(sig, frame):
    """Handle graceful shutdown."""
    app.logger.info("Received shutdown signal, shutting down gracefully...")
    sys.exit(0)

if __name__ == '__main__':
    # Register signal handlers for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    if IS_PRODUCTION and not is_vercel:
        # Use Gunicorn for production (non-Vercel)
        from gunicorn.app.base import BaseApplication
        
        class GunicornApp(BaseApplication):
            def __init__(self, app, options=None):
                self.options = options or {}
                self.application = app
                super().__init__()
            
            def load_config(self):
                for key, value in self.options.items():
                    self.cfg.set(key, value)
            
            def load(self):
                return self.application
        
        options = {
            'bind': '0.0.0.0:5000',
            'workers': WORKER_PROCESSES,
            'worker_class': 'sync',
            'worker_connections': 1000,
            'max_requests': 1000,
            'max_requests_jitter': 50,
            'timeout': 30,
            'keepalive': 2,
            'preload_app': True,
        }
        
        GunicornApp(app, options).run()
    else:
        # Use Flask development server for development or Vercel
        port = int(os.getenv('PORT', 5000))
        app.run(host='0.0.0.0', port=port, debug=not IS_PRODUCTION) 