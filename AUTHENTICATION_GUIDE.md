# 🔐 Sentari API Authentication Guide

## Current Status Summary

✅ **Working Endpoints (No Auth Required):**
- `GET /health` - Health check
- `GET /` - Root endpoint  
- `POST /api/empathy` - Empathy analysis
- `POST /api/run` - Run pipeline
- `GET /api/test-tags` - Test tag classification
- `GET /api/test-openai` - Test OpenAI (needs credits)

🔒 **Protected Endpoints (Auth Required):**
- `POST /api/analyze` - Analyze transcripts
- `POST /api/save-entry` - Save journal entries
- `POST /api/emotion-trend` - Get emotion trends
- `POST /api/pick-emoji` - Pick emoji for text
- `POST /api/pick-emoji-batch` - Batch emoji selection
- `POST /api/update-tags` - Update entry tags
- `POST /api/update-transcript` - Update transcripts

## Authentication System

Your backend uses **Supabase Authentication** with JWT tokens. Protected endpoints require:

```
Authorization: Bearer <supabase_jwt_token>
```

## How to Get Authentication Tokens

### Method 1: Supabase Dashboard (Recommended)

1. **Go to your Supabase Dashboard:**
   - URL: https://supabase.com/dashboard
   - Navigate to your project: `gfzmmnyhywerfjpnuvdj`

2. **Create a Test User:**
   - Go to `Authentication` → `Users`
   - Click `Add User`
   - Use a real email format (e.g., `<EMAIL>`)
   - Set a password
   - Click `Create User`

3. **Get the JWT Token:**
   - Click on the created user
   - Copy the `Access Token` (JWT)
   - This token can be used for API testing

### Method 2: Frontend JavaScript

```javascript
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  'https://gfzmmnyhywerfjpnuvdj.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imdmem1tbnloeXdlcmZqcG51dmRqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE1NDg2OTksImV4cCI6MjA2NzEyNDY5OX0.70brbyoXBQMyth-7wKrnBW41waN7sC4GQg5D9XRT_6U'
)

// Sign up
const { data, error } = await supabase.auth.signUp({
  email: '<EMAIL>',
  password: 'your-password'
})

// Sign in
const { data, error } = await supabase.auth.signInWithPassword({
  email: '<EMAIL>', 
  password: 'your-password'
})

// Get token
const token = data.session?.access_token
console.log('Token:', token)
```

### Method 3: Python Script

```python
from supabase import create_client

supabase = create_client(
    'https://gfzmmnyhywerfjpnuvdj.supabase.co',
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imdmem1tbnloeXdlcmZqcG51dmRqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE1NDg2OTksImV4cCI6MjA2NzEyNDY5OX0.70brbyoXBQMyth-7wKrnBW41waN7sC4GQg5D9XRT_6U'
)

# Sign in with real email
response = supabase.auth.sign_in_with_password({
    "email": "<EMAIL>",
    "password": "your-password"
})

token = response.session.access_token
print(f"Token: {token}")
```

## Testing Protected Endpoints

### cURL Examples

Replace `YOUR_JWT_TOKEN` with the actual token from Supabase:

```bash
# Analyze Transcript
curl -X POST http://localhost:5000/api/analyze \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{"transcript": "I feel happy today!", "entryId": "test-123"}' | jq .

# Save Entry
curl -X POST http://localhost:5000/api/save-entry \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{"text": "My journal entry", "language": "en"}' | jq .

# Pick Emoji
curl -X POST http://localhost:5000/api/pick-emoji \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{"text": "I am excited about my project!"}' | jq .

# Update Tags
curl -X POST http://localhost:5000/api/update-tags \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{"entryId": "test-123", "tags": ["happy", "productive"]}' | jq .

# Emotion Trend
curl -X POST http://localhost:5000/api/emotion-trend \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{"timeframe": "week"}' | jq .
```

### Python Testing

```python
import requests

# Your JWT token from Supabase
token = "YOUR_JWT_TOKEN_HERE"

headers = {
    'Content-Type': 'application/json',
    'Authorization': f'Bearer {token}'
}

# Test analyze endpoint
response = requests.post(
    'http://localhost:5000/api/analyze',
    headers=headers,
    json={
        "transcript": "I had a wonderful day today!",
        "entryId": "test-123"
    }
)

print(f"Status: {response.status_code}")
print(f"Response: {response.json()}")
```

### JavaScript/Frontend Testing

```javascript
const token = 'YOUR_JWT_TOKEN_HERE';

fetch('http://localhost:5000/api/analyze', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    transcript: 'I feel great today!',
    entryId: 'test-123'
  })
})
.then(response => response.json())
.then(data => console.log(data))
.catch(error => console.error('Error:', error));
```

## Expected Responses

### Without Authentication (401 Unauthorized)
```json
{
  "error": "Missing or invalid authorization header"
}
```

### With Invalid Token (401 Unauthorized)
```json
{
  "error": "Invalid or expired token"
}
```

### With Valid Token (200 Success)
```json
{
  "success": true,
  "analysis": {
    "purpose": "reflection",
    "tone": "positive",
    "category": "personal",
    "confidence": 0.85
  },
  "selectedTags": ["happy", "grateful"],
  "timestamp": "2025-07-06T16:30:00.000Z"
}
```

## Troubleshooting

### Issue: "Email address is invalid"
**Solution:** Use a real email format (e.g., `<EMAIL>`) instead of fake domains.

### Issue: "Invalid login credentials"
**Solution:** Create the user first through Supabase dashboard or ensure correct email/password.

### Issue: "User needs email confirmation"
**Solution:** 
- Check email for confirmation link, OR
- Disable email confirmation in Supabase Auth settings for testing

### Issue: "Database connection not available"
**Solution:** Ensure Supabase credentials are correct in `.env` file.

## Quick Test Commands

```bash
# Test without auth (should return 401)
curl -X POST http://localhost:5000/api/analyze \
  -H "Content-Type: application/json" \
  -d '{"transcript": "test"}'

# Test with invalid auth (should return 401)
curl -X POST http://localhost:5000/api/analyze \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer invalid-token" \
  -d '{"transcript": "test"}'

# Test with valid auth (should return 200)
curl -X POST http://localhost:5000/api/analyze \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_REAL_TOKEN" \
  -d '{"transcript": "I feel happy today!"}'
```

## Summary

1. ✅ **Non-protected endpoints work perfectly**
2. 🔒 **Protected endpoints correctly require authentication**
3. 🎯 **Authentication system is working as designed**
4. 📝 **To test protected endpoints, get a real JWT token from Supabase**
5. 🚀 **Your backend is ready for frontend integration**

The "404" errors you mentioned are actually **401 Unauthorized** responses, which is the correct behavior for protected endpoints without valid authentication.
